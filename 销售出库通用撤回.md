```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_universal_cancel(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_universal_cancel(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能   ： 销售出库通用撤回
 * 描述   ： 可以从销售出库流程的任何环节撤回到库存中
 *          支持的撤回环节：
 *          1. 拣货环节 (810状态) - 撤回拣货操作
 *          2. 品质扫描环节 (810状态+绑定信息) - 撤回品质扫描绑定
 *          3. 装车环节 (830状态) - 撤回装车操作
 * 参数   ： bill_no - 出库单号, sn_no - 产品条码
 * 时间   ：
 * 开发者 ：
 */
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_id text;
		_sn_no text;
		_part_no text;
		_b_id text;
		_part_qty numeric;
		_invp_area text;
		_current_status text;
		_current_status_name text;
		
		-- 拣货相关信息
		_picking_exists boolean := false;
		
		-- 品质扫描相关信息
		_qc_binding_exists boolean := false;
		_prod_09_code text;
		_hw_sn text;
		_hw_carton_sn text;
		
		-- 装车相关信息
		_loading_exists boolean := false;
		_number_plate_no text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;
	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';
		
		-- 获取用户信息
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

		-- 验证出库单存在
		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no) then
			_err_msg := format('扫描单据号【%s】不是销售出货单。', _bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
		
		select cr_dlv_h_id into _bill_id from cr_dlv_h where cr_dlv_h_no=_bill_no;
		
		-- 获取产品当前状态
		select sn_status, sn_status_name, part_no, invp_area_no 
		into _current_status, _current_status_name, _part_no, _invp_area
		from wm_sn where sn_no=_sn_no;
		
		if _part_no is null then
			_err_msg := format('产品条码【%s】不存在。', _sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		-- 验证产品状态：必须是出库流程中的状态
		if _current_status not in ('810', '820', '830') then
			_err_msg := format('产品条码【%s】当前状态【%s】不在出库流程中，不能撤回。', _sn_no, _current_status_name);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		-- 检查是否存在拣货记录
		if exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then
			_picking_exists := true;
			
			-- 获取拣货相关信息
			select cr_dlv_sn_part_rmk6, part_qty, 
				   cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3,
				   number_plate_no
			into _b_id, _part_qty, 
				 _prod_09_code, _hw_sn, _hw_carton_sn,
				 _number_plate_no
			from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no;
			
			-- 检查是否存在品质扫描绑定
			if coalesce(_prod_09_code,'') != '' or coalesce(_hw_sn,'') != '' or coalesce(_hw_carton_sn,'') != '' then
				_qc_binding_exists := true;
			end if;
			
			-- 检查是否存在装车信息
			if coalesce(_number_plate_no,'') != '' then
				_loading_exists := true;
			end if;
		else
			_err_msg := format('产品条码【%s】在出库单【%s】中没有拣货记录，不能撤回。', _sn_no, _bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		-- 开始撤回操作（按照逆向流程）
		
		-- 1. 如果存在装车信息，先撤回装车
		if _loading_exists then
			-- 清除装车信息
			update public.cr_dlv_sn_part 
			set number_plate_no = '',
				upd_time = localtimestamp,
				upd_user = _user_id,
				upd_user_no = _user_no,
				upd_user_name = _user_name
			where cr_dlv_h_id = _bill_id and sn_no = _sn_no;
			
			-- 重置出库单状态（如果还有其他产品未装车）
			if exists(select 1 from cr_dlv_sn_part 
					  where cr_dlv_h_id = _bill_id 
					  and sn_no != _sn_no
					  and coalesce(number_plate_no,'') = '') then
				update public.cr_dlv_h 
				set cr_dlv_h_rmk6 = 'OQC绑定信息完成',
					upd_time = localtimestamp,
					upd_user = _user_id,
					upd_user_no = _user_no,
					upd_user_name = _user_name
				where cr_dlv_h_id = _bill_id;
			end if;
		end if;
		
		-- 2. 如果存在品质扫描绑定，撤回绑定信息
		if _qc_binding_exists then
			-- 清除品质扫描绑定信息
			update public.cr_dlv_sn_part 
			set cr_dlv_sn_part_rmk1 = '',
				cr_dlv_sn_part_rmk2 = '',
				cr_dlv_sn_part_rmk3 = '',
				upd_time = localtimestamp,
				upd_user = _user_id,
				upd_user_no = _user_no,
				upd_user_name = _user_name
			where cr_dlv_h_id = _bill_id and sn_no = _sn_no;
			
			-- 重置出库单OQC状态（如果还有其他产品未绑定）
			if exists(select 1 from cr_dlv_sn_part 
					  where cr_dlv_h_id = _bill_id 
					  and sn_no != _sn_no
					  and coalesce(cr_dlv_sn_part_rmk1,'') = '') then
				update public.cr_dlv_h 
				set cr_dlv_h_rmk6 = '拣货完成',
					oqc_name = '',
					upd_time = localtimestamp,
					upd_user = _user_id,
					upd_user_no = _user_no,
					upd_user_name = _user_name
				where cr_dlv_h_id = _bill_id;
			end if;
		end if;
		
		-- 3. 撤回拣货操作
		if _picking_exists then
			-- 更新明细行拣货数量，并重置拣货完成状态
			update public.cr_dlv_b set 
				cr_dlv_qty = cr_dlv_qty - _part_qty,
				cr_dlv_b_rmk6 = '',
				upd_time = localtimestamp,
				upd_user = _user_id,
				upd_user_no = _user_no,
				upd_user_name = _user_name
			where cr_dlv_b_id = _b_id;

			-- 删除拣货明细记录
			delete from public.cr_dlv_sn_part 
			where cr_dlv_h_id = _bill_id and sn_no = _sn_no;

			-- 重置整单拣货完成状态（如果存在未完成的明细行）
			if exists(select 1 from cr_dlv_b where cr_dlv_h_id = _bill_id and cr_dlv_qty < cr_dlv_qty_plan) then
				update public.cr_dlv_h set 
					cr_dlv_h_rmk6 = '',
					oqc_name = '',
					upd_time = localtimestamp,
					upd_user = _user_id,
					upd_user_no = _user_no,
					upd_user_name = _user_name
				where cr_dlv_h_id = _bill_id; 
			end if;

			-- 重新计算并更新箱数统计
			update cr_dlv_b set cr_dlv_b_rmk5 = tt.ctn_num::text
			from (select tmp1.cr_dlv_h_id, tmp1.cr_dlv_b_id, coalesce(tmp2.ctn_num,0) as ctn_num
				from (select cr_dlv_b_id, cr_dlv_h_id, part_no, row_number() over(partition by part_no) as rn 
					  from cr_dlv_b where cr_dlv_h_id = _bill_id) tmp1
				left join (select part_no, count(sn_no) as ctn_num 
						   from (select distinct part_no, sn_no from cr_dlv_sn_part where cr_dlv_h_id = _bill_id) 
						   group by part_no) tmp2 on tmp2.part_no = tmp1.part_no
				where tmp1.rn = 1) tt
			where cr_dlv_b.cr_dlv_b_id = tt.cr_dlv_b_id;
		end if;

		-- 4. 恢复产品库存状态到在库
		update public.wm_sn set 
			sn_status = '800',
			sn_status_name = '在库',
			upd_time = localtimestamp,
			upd_user = _user_id,
			upd_user_no = _user_no,
			upd_user_name = _user_name
		where sn_no = _sn_no;

		-- 同时更新包装条码状态（如果存在）
		update public.wm_sn set 
			sn_status = '800',
			sn_status_name = '在库',
			upd_time = localtimestamp,
			upd_user = _user_id,
			upd_user_no = _user_no,
			upd_user_name = _user_name
		where sn_pack_50 = _sn_no;

		-- 构建撤回成功消息
		_err_msg := format('销售出库通用撤回完成，条码【%s】已从【%s】状态撤回到在库状态。', _sn_no, _current_status_name);
		
		if _loading_exists then
			_err_msg := _err_msg || format(' 已撤回装车信息（车牌号：%s）。', _number_plate_no);
		end if;
		
		if _qc_binding_exists then
			_err_msg := _err_msg || ' 已撤回品质扫描绑定信息。';
		end if;
		
		_err_msg := _err_msg || ' 已撤回拣货操作。';

		res := row('true', _err_msg);
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s', _err_msg_text, _err_pg_detail);
		res := row('false', _err_msg);
		return to_json(res);
	END;
$function$
;
```
