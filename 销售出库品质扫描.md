
```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_inspection(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_inspection(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能  ：出货检验扫描（OQC扫描）
 * 描述  ：销售出库OQC检验合格后，扫描绑定HW的相关信息; HW_SN 包含 HW_CARTON_SN 与 19_CODE
 * 时间  ：
 * 开发者：
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_bill_id text;
		_client_no text;
		_sn_no text;
		_part_no text;
	
		_prod_09_code text;
		_hw_sn text;
		_hw_carton_sn text;
	
		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;
	begin
		json_datas := json(datas);	
		_user_no := json_datas->>'user_no';
	
		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'jy_sn_no';
		_prod_09_code := json_datas->>'jy_09_code';
		_hw_sn := json_datas->>'hw_sn';
		_hw_carton_sn := json_datas->>'hw_carton_sn';

        
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		select cr_dlv_h_id,client_no into _bill_id,_client_no from cr_dlv_h where cr_dlv_h_no=_bill_no;
	
		--insert into public.a_test_log values(datas, 'pda_wms_sales_outbound_inspection', localtimestamp);
		--	res := row('true', '销售出库检验扫描完成');
		--return to_json(res);
	
		select part_no into _part_no from wm_sn where sn_no=_sn_no;
		
		--------------------------------------------
		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='拣货完成') then
			res := row('false','扫描销售出库单没有拣货完成，不能绑定出货信息。');
			return to_json(res);
		end if;

		if not exists(select distinct 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then
			res := row('false','扫描出货通知单号与产品SN不匹配(不属于此销售出库单的拣货产品)。');
			return to_json(res);
		end if;

		if not exists(select distinct 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no 
				and coalesce(cr_dlv_sn_part_rmk1,'')='' and coalesce(cr_dlv_sn_part_rmk2,'')='' and coalesce(cr_dlv_sn_part_rmk3,'')='') then
			res := row('false', '扫描产品已经绑定信息，不能二次绑定。');
			return to_json(res);
		end if;
		-------------------------------------------------------
		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='810') then 
			res := row('false', '此产品不是待OQC检验状态，请核对。');
			return to_json(res);
		end if;
		----------------------------------------------------
		/*if strpos(_sn_no, concat('19',substring(split_part(_part_no,_client_no,1),2)))<>1 then
			res := row('false', '产品19码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');
			return to_json(res);
		end if;
	
		if strpos(_prod_09_code, concat('09',substring(split_part(_part_no,_client_no,1),2)))<>1 then
			res := row('false', '产品09码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');
			return to_json(res);
		end if;*/
		
		if strpos(_hw_sn, _hw_carton_sn)=0 then
			res := row('false', '扫描华为二维码与 HW外箱码不一致。');
			return to_json(res);
		end if;
	
		if strpos(_hw_sn, _sn_no)=0 then
			res := row('false', '扫描华为二维码与产品19码（金洋包装条码）不一致。');
			return to_json(res);
		end if;
		---------------------------------------------------	
		/*
		if not exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then
			res := row('false','此产品没有销售出库拣货，不能做出货检验。');
			return to_json(res);
		end if;
		
		------无需生成OQC出货检验单--------
		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
						where a.move_order_id=_bill_id and b.sn_no=_sn_no) then
			res := row('false', '此产品没有OQC的检验记录，不能做出库扫描');
			return to_json(res);
		end if;
		*/
		
		if exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and cr_dlv_sn_part_rmk2=_hw_sn) then
			res := row('false', '此华为SN已经绑定，不能二次绑定。');
			return to_json(res);
		end if;
	
		if exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and cr_dlv_sn_part_rmk3=_hw_carton_sn) then
			res := row('false', '此华为卡通箱号已经绑定，不能二次绑定。');
			return to_json(res);
		end if;
		--------------------------------------------------------------------------------------------

		update public.cr_dlv_sn_part set cr_dlv_sn_part_rmk1=_prod_09_code,cr_dlv_sn_part_rmk2=_hw_sn,cr_dlv_sn_part_rmk3=_hw_carton_sn,
			upd_time=localtimestamp
		where sn_no=_sn_no and cr_dlv_h_id=_bill_id;	
	
		if not exists(select distinct 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_sn_part_rmk1,'')='') then
			update public.cr_dlv_h set cr_dlv_h_rmk6='OQC绑定信息完成',oqc_name=_user_name
			where cr_dlv_h_no=_bill_no;
		end if;
	
		/*
		update public.wm_sn set sn_status='820',sn_status_name='OQC检验',upd_time=localtimestamp
		where sn_no=_sn_no;
		*/
		res := row('true', '销售出库检验扫描完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);	

	END;
$function$
;

```