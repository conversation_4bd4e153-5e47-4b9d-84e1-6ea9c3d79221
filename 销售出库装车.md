
```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_loading(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_loading(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：销售出库装车
 * 描述：品质扫描完成后的装车环节，更新库存状态为830，单据状态为出库完成
 * 参数：number_plate_no - 车牌号
 *      bill_no - 出库单号
 *      sn_no - 产品条码
 * 时间：
 * 开发者：
 */
declare 
    json_datas json;
    _user_id text;
    _user_no text;
    _user_name text;
    _host text;

    _bill_id text;
    _client_no text;
    _part_no text;
    _part_qty numeric;
    _sn_type text;
    _invp_area text;

    _err_msg_text text;
    _err_pg_detail text;
    _err_msg text;
    _number_plate_no text;
  	_bill_no text;
    _sn_no text;
     res returntype;
begin
     json_datas := json(datas);	
     _user_no := json_datas->>'user_no';
	 json_datas := json(json_datas->'datas'->0);
     _number_plate_no := json_datas->>'number_plate_no';
	 _bill_no:= json_datas->>'bill_no';
	 _sn_no := json_datas->>'sn_no';
  
     res := row('false', '暂时停使用');
     return to_json(res); 
  
    -- 参数验证
    if coalesce(_number_plate_no, '') = '' then
        res := row('false', '车牌号不能为空');
        return to_json(res);
    end if;
    
    if coalesce(_bill_no, '') = '' then
        res := row('false', '出库单号不能为空');
        return to_json(res);
    end if;
    
    if coalesce(sn_no, '') = '' then
        res := row('false', '产品条码不能为空');
        return to_json(res);
    end if;

    -- 获取用户信息（这里需要根据实际情况获取当前用户）
    select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

    -- 验证出库单状态
    if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=bill_no and coalesce(cr_dlv_h_rmk6,'')='OQC绑定信息完成') then
        res := row('false', format('出库单【%s】不存在或状态不正确，必须是OQC绑定信息完成状态才能装车', _bill_no));
        return to_json(res);
    end if;

    -- 获取出库单信息
    select cr_dlv_h_id, client_no into _bill_id, _client_no 
    from cr_dlv_h where cr_dlv_h_no = _bill_no;

    -- 获取产品信息
    select part_no, part_qty, sn_type, invp_area_no 
    into _part_no, _part_qty, _sn_type, _invp_area  
    from wm_sn where sn_no = _sn_no;

    if _part_no is null then
        res := row('false', format('产品条码【%s】不存在！', _sn_no));
        return to_json(res);
    end if;

    -- 验证产品是否属于该出库单
    if not exists(select 1 from cr_dlv_sn_part 
                  where cr_dlv_h_id = _bill_id and sn_no = _sn_no) then
        res := row('false', format('产品条码【%s】不属于出库单【%s】', _sn_no, _bill_no));
        return to_json(res);
    end if;

    -- 验证产品是否已完成品质扫描绑定
    if not exists(select 1 from cr_dlv_sn_part 
                  where cr_dlv_h_id = _bill_id and sn_no = _sn_no 
                  and coalesce(cr_dlv_sn_part_rmk1,'') != '' 
                  and coalesce(cr_dlv_sn_part_rmk2,'') != '' 
                  and coalesce(cr_dlv_sn_part_rmk3,'') != '') then
        res := row('false', format('产品条码【%s】未完成品质扫描绑定，不能装车', _sn_no));
        return to_json(res);
    end if;

    -- 验证库存状态
    if not exists(select 1 from wm_sn where sn_no = _sn_no and sn_status = '820') then
        res := row('false', format('产品条码【%s】库存状态不正确，必须是820（OQC检验）状态才能装车', _sn_no));
        return to_json(res);
    end if;

    -- 验证是否已装车
    if exists(select 1 from cr_dlv_sn_part 
              where cr_dlv_h_id = _bill_id and sn_no = _sn_no 
              and coalesce(number_plate_no,'') != '') then
        res := row('false', format('产品条码【%s】已装车，车牌号：%s', _sn_no, 
                   (select number_plate_no from cr_dlv_sn_part 
                    where cr_dlv_h_id = _bill_id and sn_no = _sn_no limit 1)));
        return to_json(res);
    end if;

    -- 更新装车信息
    update public.cr_dlv_sn_part 
    set number_plate_no = _number_plate_no,
        upd_time = localtimestamp,
        upd_user = _user_id,
        upd_user_no = _user_no,
        upd_user_name = _user_name
    where cr_dlv_h_id = _bill_id and sn_no = _sn_no;

    -- 更新库存状态为830（装车）
    update public.wm_sn 
    set sn_status = '830',
        sn_status_name = '出库',
        upd_time = localtimestamp,
        upd_user = _user_id,
        upd_user_no = _user_no,
        upd_user_name = _user_name
    where sn_no = _sn_no;

    -- 更新库存状态为830（装车）
    update public.wm_sn 
    set sn_status = '830',
        sn_status_name = '出库',
        upd_time = localtimestamp,
        upd_user = _user_id,
        upd_user_no = _user_no,
        upd_user_name = _user_name
    where sn_pack_50 = _sn_no;

    -- 检查是否所有产品都已装车完成
    if not exists(select 1 from cr_dlv_sn_part 
                  where cr_dlv_h_id = _bill_id 
                  and coalesce(number_plate_no,'') = '') then
        -- 所有产品都已装车，更新出库单状态为出库完成
        
        update public.cr_dlv_h 
        set cr_dlv_h_rmk6 = '出库完成',
            upd_time = localtimestamp,
            upd_user = _user_id,
            upd_user_no = _user_no,
            upd_user_name = _user_name
        where cr_dlv_h_id = _bill_id;

    end if;

    res := row('true', format('产品【%s】装车成功，车牌号：%s', _sn_no, _number_plate_no));
    return to_json(res);

EXCEPTION WHEN OTHERS THEN 
    GET STACKED DIAGNOSTICS 
        _err_msg_text = MESSAGE_TEXT,
        _err_pg_detail = PG_EXCEPTION_DETAIL;

    _err_msg := format('错误信息:%s,详情:%s', _err_msg_text, _err_pg_detail);
    res := row('false', _err_msg);
    return to_json(res);	

END;
$function$
;
```