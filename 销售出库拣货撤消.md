```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_picking_cancel_general(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_picking_cancel_general(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能   ： 销售出库拣货撤回
 * 描述   ： 撤回已拣货的产品，恢复到拣货前状态
 * 时间   ：
 * 开发者 ：
 */
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_id text;
		_sn_no text;
		_part_no text;
		_b_id text;
		_part_qty numeric;
		_invp_area text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
		res returntype;
	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';

		-- 获取用户信息
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

		-- 验证产品状态：必须是已拣货状态
		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='810') then
			_err_msg := format('条码【%s】不是已拣货状态，不能撤回。', _sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		-- 验证出库单存在
		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no) then
			_err_msg := format('扫描单据号【%s】不是销售出货单。', _bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		select cr_dlv_h_id into _bill_id from cr_dlv_h where cr_dlv_h_no=_bill_no;

		-- 验证拣货记录存在
		if not exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then
			_err_msg := format('拣货撤回扫描的单据号【%s】与产品条码【%s】不匹配。', _bill_no, _sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		-- 获取拣货记录信息
		select cr_dlv_sn_part_rmk6,part_qty,part_no,invp_no into _b_id,_part_qty,_part_no,_invp_area
		from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no;

		-- 更新明细行拣货数量，并重置拣货完成状态
		update public.cr_dlv_b set
			cr_dlv_qty=cr_dlv_qty-_part_qty,
			cr_dlv_b_rmk6='',
			upd_time=localtimestamp,
			upd_user=_user_id,
			upd_user_no=_user_no,
			upd_user_name=_user_name
		where cr_dlv_b_id=_b_id;

		-- 删除拣货明细记录
		delete from public.cr_dlv_sn_part
		where cr_dlv_h_id=_bill_id and sn_no=_sn_no;

		-- 重置整单拣货完成状态（如果存在未完成的明细行）
		if exists(select 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and cr_dlv_qty < cr_dlv_qty_plan) then
			update public.cr_dlv_h set
				cr_dlv_h_rmk6='',
				upd_time=localtimestamp,
				upd_user=_user_id,
				upd_user_no=_user_no,
				upd_user_name=_user_name
			where cr_dlv_h_id=_bill_id;
		end if;

		-- 重新计算并更新箱数统计
		update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
		from (select tmp1.cr_dlv_h_id,tmp1.cr_dlv_b_id,coalesce(tmp2.ctn_num,0) as ctn_num
			from (select cr_dlv_b_id,cr_dlv_h_id,part_no, row_number() over(partition by part_no) as rn from cr_dlv_b where cr_dlv_h_id =_bill_id) tmp1
			left join (select part_no,count(sn_no) as ctn_num from (select distinct part_no,sn_no from cr_dlv_sn_part where cr_dlv_h_id =_bill_id) group by part_no) tmp2 on tmp2.part_no=tmp1.part_no
			where tmp1.rn=1) tt
		where cr_dlv_b.cr_dlv_b_id=tt.cr_dlv_b_id;

		-- 恢复产品库存状态
		update public.wm_sn set
			sn_status='800',
			sn_status_name='在库',
			upd_time=localtimestamp,
			upd_user=_user_id,
			upd_user_no=_user_no,
			upd_user_name=_user_name
		where sn_no=_sn_no;

		_err_msg := format('销售出库拣货撤回完成，条码【%s】已恢复到在库状态。', _sn_no);
		res := row('true', _err_msg);
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN
		GET STACKED DIAGNOSTICS
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;

		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_pg_detail);
		res := row('false',_err_msg);
		return to_json(res);
	END;
$function$
;

```

