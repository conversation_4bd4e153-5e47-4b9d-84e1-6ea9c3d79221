```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_picking_cancel_general(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_picking_cancel_general(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
*销售出库撤回
*
*/
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_id text;
		_sn_no text;
		_b_id text;
		_part_qty numeric;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;
	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';
		

		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='810') then
			res := row('false', '拣货撤消产品不是已拣货状态。');
			return to_json(res);
		end if;		

		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no) then
			res := row('false', '扫描单据号不是销售出货单。');
			return to_json(res);
		end if;
		select cr_dlv_h_id into _bill_id from cr_dlv_h where cr_dlv_h_no=_bill_no;
		if not exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then
			res := row('false', '拣货撤消扫描的单据号与产品不匹配。');
			return to_json(res);
		end if;
		
		select cr_dlv_sn_part_rmk6,part_qty into _b_id,_part_qty from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no;
		
		if exists(select 1 from cr_dlv_b where cr_dlv_b_id=_b_id and coalesce(cr_dlv_b_rmk6,'')='拣货完成') then
			update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty-_part_qty,cr_dlv_b_rmk6='',upd_time=localtimestamp
			where cr_dlv_b_id=_b_id;
		else
			update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty-_part_qty,upd_time=localtimestamp
			where cr_dlv_b_id=_b_id;
		end if;

		if exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='拣货完成') then
			update public.cr_dlv_h set cr_dlv_h_rmk6=''
			where cr_dlv_h_no=_bill_no; 
		end if;

		update public.wm_sn set sn_status='800',sn_status_name='在库',upd_time=localtimestamp
		where sn_no=_sn_no;

		res := row('true', '销售出库撤消完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);
	END;
$function$
;

```

